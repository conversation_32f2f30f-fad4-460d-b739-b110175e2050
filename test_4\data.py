# 🚀 项目一：高级数据处理与分析报告 (结合 Pandas 与 Excel)
# 这个项目将让您充分体验 Pandas 在数据清洗、分析和报告生成方面的强大能力，它能处理您之前项目一或项目二中收集到的数据。

# 核心技能： 数据清洗、数据转换、数据聚合、Excel 多工作表输出
# 主要库： pandas, openpyxl
# 实施步骤：
# 数据来源：
# 使用您在**项目一（本地文件整理与报告生成器）**中生成的 organization_report.xlsx 文件。
# 或者，使用您在**项目二（简单网页内容提取器）**中生成的 tech_news.xlsx 文件。
# 如果这两个文件数据量较少，您可以手动向其中添加更多行数据，使其更具代表性。
# 编写Python脚本 (advanced_data_processor.py)：
# 读取数据： 使用 pandas.read_excel() 读取上述 Excel 文件到一个 DataFrame。
# 数据清洗与转换：
# 如果您处理的是 organization_report.xlsx：确保"整理时间"列是日期时间格式 (pd.to_datetime())。尝试添加一列"文件大小（KB）"，可以手动输入一些假数据或用 os.path.getsize() （更高级）获取。
# 如果您处理的是 tech_news.xlsx：检查是否有重复的新闻链接，并使用 df.drop_duplicates() 进行去重。尝试标准化标题的格式（例如，全部转为小写）。
# 数据分析与聚合：
# 如果您处理的是 organization_report.xlsx：按"文件类型 (扩展名)"进行分组，统计每种类型的文件数量。找出文件数量最多的三种类型。
# 如果您处理的是 tech_news.xlsx：如果您能提取到发布日期，尝试按日期分组，统计每天发布的新闻数量。
# 输出到新Excel文件：
# 将原始数据、清洗后的数据以及分析聚合结果（例如，文件类型统计表、每日新闻数量表）分别保存到同一个新 Excel 文件 (processed_report.xlsx 或 analyzed_news.xlsx) 的不同工作表中。
# 学习成果：
# 深入掌握 Pandas 的数据读取、写入、清洗、转换（如日期时间转换、去重）。
# 熟练运用 Pandas 进行数据分组 (groupby)、聚合 (count, sum, mean) 和排序 (sort_values)。
# 学会将 DataFrame 数据导出到 Excel 的不同工作表。
# 理解 Pandas 在数据分析流程中的核心作用。
import pandas as pd
import openpyxl

data=r'./test_2/tech_news.xlsx'

# 基本读取方式
df = pd.read_excel(data)

# 1. 指定工作表
# df = pd.read_excel(data, sheet_name='Sheet1')  # 通过名称指定工作表
# df = pd.read_excel(data, sheet_name=0)         # 通过索引指定工作表（0表示第一个工作表）

# 2. 指定列名
# df = pd.read_excel(data, names=['A', 'B', 'C'])  # 自定义列名

# 3. 指定要读取的列
# df = pd.read_excel(data, usecols=['标题', '内容'])  # 只读取指定的列

# 4. 指定数据类型
# df = pd.read_excel(data, dtype={'日期': 'datetime64', '数量': 'int64'})

# 5. 跳过某些行
# df = pd.read_excel(data, skiprows=2)  # 跳过前2行

# 6. 只读取特定行数
# df = pd.read_excel(data, nrows=100)  # 只读取前100行

# 查看读取的数据
print("数据维度:", df.shape)  # 显示行数和列数
print("\n列名:", df.columns.tolist())  # 显示所有列名
print("\n数据预览:\n", df.head())  # 显示前5行数据

# # 1. 查看数据基本信息
# print("数据维度:", df.shape)  # 显示行数和列数
# print("\n列名:", df.columns.tolist())  # 显示所有列名
# print("\n数据类型:\n", df.dtypes)  # 显示每列的数据类型
# print("\n数据预览:\n", df.head())  # 显示前5行数据

# 2. 数据筛选示例
# 假设我们要筛选特定条件的数据
# 例如：筛选标题包含特定关键词的行
filtered_df = df[df['内容'].str.contains('协议')]
print("\n数据预览:\n", filtered_df.head())  # 显示前5行数据

# 3. 数据转换示例
# 例如：将日期列转换为datetime类型
# df['日期'] = pd.to_datetime(df['日期'])

# 4. 数据分组和聚合示例
# 例如：按某列分组并计算统计值
# grouped_df = df.groupby('分组列').agg({
#     '数值列1': 'mean',
#     '数值列2': 'count'
# })

# 5. 数据清洗示例
# 例如：删除重复行
# df = df.drop_duplicates()
# 例如：处理缺失值
# df = df.fillna(0)  # 用0填充缺失值
