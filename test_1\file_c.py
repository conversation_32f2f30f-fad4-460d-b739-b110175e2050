# 创建练习环境：
# 在您的电脑上创建一个名为 ToSort_Files 的文件夹。
# 在 ToSort_Files 文件夹内，手动创建一些不同类型的文件（例如，5个.txt 文件, 3个.jpg 文件, 2个.docx 文件, 2个.xlsx 文件）。您可以随意命名它们，内容为空或随意填写一些字符即可。
# 编写Python脚本 (file_organizer.py)：
# 脚本首先检查是否存在一个名为 Organized_Files 的目标文件夹，如果不存在则创建它。
# 遍历 ToSort_Files 文件夹中的所有文件。

import os
import shutil

# folder='ToSort_Files'
# shutil.rmtree(folder)
parrent_folder='test_1'
sub_folder='ToSort_Files'
ful_folder=os.path.join(parrent_folder,sub_folder)
os.makedirs(os.path.join(parrent_folder,sub_folder),exist_ok=True)



# 2. 创建 5 个 .txt 文件
for i in range(1, 6):
    with open(os.path.join(ful_folder, f'test_{i}.txt'), 'w', encoding='utf-8') as f:
        f.write(f'这是第{i}个txt文件')

# 3. 创建 3 个 .jpg 文件（内容为空）
for i in range(1, 4):
    with open(os.path.join(ful_folder, f'image_{i}.jpg'), 'wb') as f:
        pass  # 空文件

# 4. 创建 2 个 .docx 文件（内容为空）
for i in range(1, 3):
    with open(os.path.join(ful_folder, f'doc_{i}.docx'), 'wb') as f:
        pass  # 空文件

# 5. 创建 2 个 .xlsx 文件（内容为空）
for i in range(1, 3):
    with open(os.path.join(ful_folder, f'sheet_{i}.xlsx'), 'wb') as f:
        pass  # 空文件

print("文件已创建完毕！")





