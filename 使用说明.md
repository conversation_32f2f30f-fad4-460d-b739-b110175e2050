# 数据库台账导出工具使用说明

## 功能介绍
这是一个自动化工具，用于从数据库客户端软件中导出台账数据并保存为Excel文件。

## 主要功能
- 自动控制键盘输入（↓键 + Ctrl+C）
- 智能检测数据末尾（通过重复内容判断）
- 实时显示收集进度
- 导出数据到Excel文件
- 友好的GUI界面

## 安装和运行

### 方法一：直接运行Python脚本
1. 确保已安装Python 3.7+
2. 安装依赖包：
   ```
   pip install -r requirements.txt
   ```
3. 运行程序：
   ```
   python database_exporter.py
   ```

### 方法二：打包为exe文件
1. 双击运行 `build_exe.bat`
2. 等待打包完成
3. 在 `dist` 文件夹中找到生成的exe文件

## 使用步骤

1. **准备工作**
   - 打开目标数据库客户端软件
   - 将光标定位到数据表的第一行
   - 确保当前选中的是需要导出的数据行

2. **配置设置**
   - 设置操作间隔（建议0.5-1秒）
   - 选择输出文件路径和名称

3. **开始导出**
   - 点击"开始导出"按钮
   - 程序会倒计时5秒，请在此期间切换到目标软件窗口
   - 程序开始自动执行：
     - 按↓键移动到下一行
     - 按Ctrl+C复制当前行数据
     - 重复上述步骤

4. **自动停止**
   - 当检测到连续3次相同内容时，程序认为已到达数据末尾
   - 自动停止并保存数据到Excel文件

## 注意事项

1. **安全设置**
   - 程序启用了pyautogui的安全模式
   - 将鼠标移动到屏幕左上角可紧急停止程序

2. **使用环境**
   - 确保目标软件支持Ctrl+C复制功能
   - 建议在稳定的网络环境下使用
   - 导出过程中不要操作键盘和鼠标

3. **数据格式**
   - 程序会自动去除空白数据
   - 每行数据作为Excel中的一行记录
   - 支持中文和特殊字符

## 故障排除

1. **程序无法启动**
   - 检查Python环境是否正确安装
   - 确认所有依赖包已安装

2. **无法复制数据**
   - 确认目标软件支持Ctrl+C操作
   - 检查是否有其他程序占用剪贴板

3. **数据重复或丢失**
   - 调整操作间隔时间
   - 确认目标软件响应速度

## 技术支持
如有问题，请检查日志输出获取详细错误信息。
