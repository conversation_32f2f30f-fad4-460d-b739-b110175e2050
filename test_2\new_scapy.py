# 实施步骤：
# 选择目标网站： 选择一个结构相对简单的科技新闻网站或博客列表页面 (例如：OSCHINA 的 "最近更新" 资讯版块 或 InfoQ 的推荐文章)。
# 友情提示：请确保您的爬取行为遵守网站的 robots.txt 规则，并且不要过于频繁地请求，以免给对方服务器造成负担。
# 编写Python脚本 (news_scraper.py)：
# 使用 requests.get() 方法获取目标网页的HTML内容。
# 使用 BeautifulSoup(html_content, 'html.parser') 解析HTML。
# 分析网页结构： 打开浏览器，使用开发者工具（通常按F12）检查您想要提取的新闻标题和链接在HTML中的标签和类名。
# 使用 BeautifulSoup 的 find_all() 或 select() 方法定位并提取所有新闻的标题和对应的链接。
# （可选）尝试提取发布日期或简短摘要。
# 将提取到的数据（标题、链接、日期/摘要）存储到一个名为 tech_news.xlsx 的Excel文件中，每条新闻占一行。

import requests
from bs4 import BeautifulSoup
import pandas as pd
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import os

# 设置输出目录
output_dir = r'C:\Users\<USER>\.cursor\project\2、test\test_2'
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

# 设置Chrome选项
chrome_options = Options()
chrome_options.add_argument('--disable-gpu')
chrome_options.add_argument('--no-sandbox')
chrome_options.add_argument('--disable-dev-shm-usage')

try:
    # 初始化Chrome浏览器
    driver = webdriver.Chrome(options=chrome_options)
    
    # 访问网页
    print("正在访问网页...")
    driver.get('https://www.oschina.net/news/project')
    
    # 等待页面加载
    time.sleep(2)
    
    # 记录上一次的新闻数量
    last_count = 0
    scroll_attempts = 0
    max_attempts = 10  # 最大滚动次数
    
    while scroll_attempts < max_attempts:
        # 获取当前页面高度
        last_height = driver.execute_script("return document.body.scrollHeight")
        
        # 滚动到页面底部
        driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        
        # 等待新内容加载
        time.sleep(2)
        
        # 获取新的页面高度
        new_height = driver.execute_script("return document.body.scrollHeight")
        
        # 获取当前新闻数量
        news_items = driver.find_elements(By.CLASS_NAME, 'news-item')
        current_count = len(news_items)
        
        print(f"当前找到 {current_count} 条新闻")
        
        # 如果新闻数量没有增加，可能已经到底了
        if current_count == last_count:
            scroll_attempts += 1
        else:
            scroll_attempts = 0  # 重置尝试次数
            last_count = current_count
        
        # 如果页面高度没有变化，可能已经到底了
        if new_height == last_height:
            scroll_attempts += 1
        
        # 如果已经找到足够多的新闻，可以停止
        if current_count >= 80:
            break
    
    # 获取页面源码
    html_content = driver.page_source
    
    # 使用BeautifulSoup解析HTML
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 查找所有新闻条目
    news_items = soup.find_all('div', class_='item news-item news-item-hover')
    print(f"\n总共找到 {len(news_items)} 条新闻")
    
    # 存储所有新闻数据
    news_data = []
    
    # 提取每条新闻的信息
    print("\n正在提取新闻信息...")
    print("-" * 50)
    
    for index, item in enumerate(news_items, 1):
        # 获取新闻URL
        news_url = item.get('data-url', '')
        
        # 获取标题
        title_div = item.find('div', class_='title')
        title = title_div.text.strip() if title_div else ''
        
        # 获取新闻内容
        description_div = item.find('div', class_='description')
        content = ''
        if description_div:
            content_p = description_div.find('p', class_='line-clamp')
            content = content_p.text.strip() if content_p else ''
        
        # 将数据添加到列表
        news_data.append({
            '标题': title,
            '链接': news_url,
            '内容': content
        })
        
        print(f"已提取第 {index} 条新闻: {title[:30]}...")
    
    # 将数据保存到Excel文件
    if news_data:
        df = pd.DataFrame(news_data)
        # 设置输出文件路径
        csv_file = os.path.join(output_dir, 'tech_news.csv')
        excel_file = os.path.join(output_dir, 'tech_news.xlsx')
        
        # 保存为CSV文件
        df.to_csv(csv_file, index=False, encoding='utf-8-sig')
        print(f"\n成功保存 {len(news_data)} 条新闻到 {csv_file}")
        
        # 保存为Excel文件
        df.to_excel(excel_file, index=False, engine='openpyxl')
        print(f"成功保存 {len(news_data)} 条新闻到 {excel_file}")
    
    # 关闭浏览器
    driver.quit()
    
except Exception as e:
    print(f"发生错误: {e}")
    # 确保浏览器被关闭
    try:
        driver.quit()
    except:
        pass



