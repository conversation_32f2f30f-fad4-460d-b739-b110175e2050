import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pyautogui
import pyperclip
import pandas as pd
import time
import threading
from datetime import datetime
import os

class DatabaseExporter:
    def __init__(self, root):
        self.root = root
        self.root.title("数据库台账导出工具")
        self.root.geometry("600x500")
        self.root.resizable(False, False)
        
        # 控制变量
        self.is_running = False
        self.collected_data = []
        self.last_clipboard_content = ""
        self.duplicate_count = 0
        self.max_duplicates = 3  # 连续重复次数阈值
        
        # 设置变量
        self.delay_var = tk.DoubleVar(value=0.5)
        self.output_path_var = tk.StringVar(value="导出数据.xlsx")
        
        self.setup_ui()
        
    def setup_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="数据库台账自动导出工具", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 设置区域
        settings_frame = ttk.LabelFrame(main_frame, text="设置", padding="10")
        settings_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 延迟设置
        ttk.Label(settings_frame, text="操作间隔(秒):").grid(row=0, column=0, sticky=tk.W)
        delay_spinbox = ttk.Spinbox(settings_frame, from_=0.1, to=5.0, increment=0.1,
                                   textvariable=self.delay_var, width=10)
        delay_spinbox.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # 输出文件设置
        ttk.Label(settings_frame, text="输出文件:").grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        output_entry = ttk.Entry(settings_frame, textvariable=self.output_path_var, width=30)
        output_entry.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))
        
        browse_btn = ttk.Button(settings_frame, text="浏览", command=self.browse_output_file)
        browse_btn.grid(row=1, column=2, padx=(10, 0), pady=(10, 0))
        
        # 控制按钮区域
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=2, column=0, columnspan=3, pady=(10, 0))
        
        self.start_btn = ttk.Button(control_frame, text="开始导出", command=self.start_export)
        self.start_btn.grid(row=0, column=0, padx=(0, 10))
        
        self.stop_btn = ttk.Button(control_frame, text="停止", command=self.stop_export, state="disabled")
        self.stop_btn.grid(row=0, column=1, padx=(0, 10))
        
        self.clear_btn = ttk.Button(control_frame, text="清空数据", command=self.clear_data)
        self.clear_btn.grid(row=0, column=2)
        
        # 状态显示
        status_frame = ttk.LabelFrame(main_frame, text="状态", padding="10")
        status_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
        self.status_label = ttk.Label(status_frame, text="就绪")
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
        self.progress_var = tk.StringVar(value="已收集: 0 条数据")
        self.progress_label = ttk.Label(status_frame, textvariable=self.progress_var)
        self.progress_label.grid(row=1, column=0, sticky=tk.W)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="日志", padding="10")
        log_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        
        # 创建滚动文本框
        self.log_text = tk.Text(log_frame, height=15, width=70)
        scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(4, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
    def browse_output_file(self):
        filename = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
        )
        if filename:
            self.output_path_var.set(filename)
    
    def log_message(self, message):
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def start_export(self):
        if self.is_running:
            return
            
        self.is_running = True
        self.start_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        self.status_label.config(text="运行中...")
        
        self.log_message("开始导出数据...")
        self.log_message("请确保目标软件窗口处于活动状态")
        self.log_message("5秒后开始自动操作...")
        
        # 在新线程中运行导出逻辑
        export_thread = threading.Thread(target=self.export_data)
        export_thread.daemon = True
        export_thread.start()
    
    def stop_export(self):
        self.is_running = False
        self.start_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.status_label.config(text="已停止")
        self.log_message("用户停止了导出操作")
    
    def clear_data(self):
        self.collected_data.clear()
        self.progress_var.set("已收集: 0 条数据")
        self.log_message("已清空收集的数据")
    
    def export_data(self):
        try:
            # 等待5秒让用户准备
            for i in range(5, 0, -1):
                if not self.is_running:
                    return
                self.log_message(f"倒计时: {i} 秒")
                time.sleep(1)
            
            self.log_message("开始自动操作...")
            delay = self.delay_var.get()
            
            while self.is_running:
                # 按下向下箭头键
                pyautogui.press('down')
                time.sleep(delay)
                
                # 按下Ctrl+C复制
                pyautogui.hotkey('ctrl', 'c')
                time.sleep(delay)
                
                # 获取剪贴板内容
                try:
                    current_content = pyperclip.paste()
                    
                    if current_content == self.last_clipboard_content:
                        self.duplicate_count += 1
                        self.log_message(f"检测到重复内容 ({self.duplicate_count}/{self.max_duplicates})")
                        
                        if self.duplicate_count >= self.max_duplicates:
                            self.log_message("达到重复阈值，认为已到达数据末尾")
                            break
                    else:
                        self.duplicate_count = 0
                        if current_content.strip():  # 只添加非空内容
                            self.collected_data.append(current_content.strip())
                            self.progress_var.set(f"已收集: {len(self.collected_data)} 条数据")
                            self.log_message(f"收集数据: {current_content[:50]}...")
                        
                        self.last_clipboard_content = current_content
                
                except Exception as e:
                    self.log_message(f"获取剪贴板内容失败: {str(e)}")
                
                time.sleep(delay)
            
            # 导出完成，保存数据
            if self.collected_data:
                self.save_to_excel()
            else:
                self.log_message("没有收集到数据")
                
        except Exception as e:
            self.log_message(f"导出过程中发生错误: {str(e)}")
        finally:
            self.is_running = False
            self.start_btn.config(state="normal")
            self.stop_btn.config(state="disabled")
            self.status_label.config(text="完成")
    
    def save_to_excel(self):
        try:
            output_path = self.output_path_var.get()
            
            # 创建DataFrame
            df = pd.DataFrame(self.collected_data, columns=['数据'])
            
            # 保存到Excel
            df.to_excel(output_path, index=False, engine='openpyxl')
            
            self.log_message(f"数据已保存到: {output_path}")
            self.log_message(f"共导出 {len(self.collected_data)} 条数据")
            
            messagebox.showinfo("导出完成", f"数据已成功导出到:\n{output_path}\n\n共 {len(self.collected_data)} 条数据")
            
        except Exception as e:
            error_msg = f"保存文件失败: {str(e)}"
            self.log_message(error_msg)
            messagebox.showerror("错误", error_msg)

def main():
    # 设置pyautogui安全设置
    pyautogui.FAILSAFE = True
    pyautogui.PAUSE = 0.1
    
    root = tk.Tk()
    app = DatabaseExporter(root)
    root.mainloop()

if __name__ == "__main__":
    main()
