# 选择测试网站： 使用一个专门用于测试自动化的网站，例如：
# https://the-internet.herokuapp.com/login (一个包含登录表单的测试页面)
# 或您自己创建一个简单的本地HTML文件包含一些输入框和按钮。
# 重要提示：初期练习请勿直接在重要的、真实的账户上进行自动化操作，以免产生非预期后果。
# 编写Python脚本 (selenium_login_test.py)：
# 导入 selenium.webdriver。
# 初始化 WebDriver (例如 driver = webdriver.Chrome())。
# 使用 driver.get("目标网址") 打开测试网页。
# 定位元素： 使用开发者工具找到用户名输入框、密码输入框和登录按钮的ID、name、XPath或CSS selector。
# 使用 driver.find_element() 方法定位这些元素。
# 使用 element.send_keys("一些文本") 向输入框输入虚拟的用户名和密码。
# 使用 element.click() 点击登录按钮。
# （可选）添加 time.sleep() 来观察每一步操作，或者尝试获取页面上的文本来验证操作是否成功（例如，登录失败的提示信息）。
# 最后使用 driver.quit() 关闭浏览器。

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time

chrome_options=Options()
chrome_options.add_argument('--disable-gpu')
chrome_options.add_argument('--no-sandbox')
chrome_options.add_argument('--disable-dev-shm-usage')
chrome_options.add_argument('--start-maximized') 

driver = webdriver.Chrome(options=chrome_options)
driver.get('https://the-internet.herokuapp.com/login')

try:
    # 等待用户名输入框可见
    username = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.ID, "username"))
    )
    username.clear()
    username.send_keys("admin")

    # 等待密码输入框可见
    password = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.ID, "password"))
    )
    password.clear()
    password.send_keys("12345678")

    # 等待登录按钮可点击
    login_button = WebDriverWait(driver, 10).until(
        EC.element_to_be_clickable((By.XPATH, '//*[@id="login"]/button/i'))
    )
    login_button.click()

    # 等待用户输入，保持浏览器窗口打开
    input("按回车键关闭浏览器...")

except Exception as e:
    print(f"发生错误: {str(e)}")
finally:
    driver.quit()







