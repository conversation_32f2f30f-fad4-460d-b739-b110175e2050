<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Transcript</title>
    <style>
        body {
            background-color: #e0e0e0; /* Light gray background for contrast with the "page" */
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px 0; /* Add some padding to top/bottom of viewport */
            font-size: 10px;
            line-height: 1.3;
        }
        .a4-container {
            background-color: #fff;
            width: 210mm; /* A4 width */
            min-height: 270mm; /* Approximate A4 height, content will push it */
            margin: 20px auto;
            padding: 15mm; /* Page margins */
            box-sizing: border-box;
            box-shadow: 0 0 15px rgba(0,0,0,0.2);
            overflow-x: auto; /* Prevent content from breaking layout too easily */
        }
        .header {
            text-align: center;
            margin-bottom: 15px;
        }
        .header h1 {
            font-size: 18px;
            margin: 0 0 2px 0;
            font-weight: bold;
        }
        .header p {
            font-size: 12px;
            margin: 2px 0;
            font-weight: bold;
        }

        .info-grid {
            display: grid;
            /*grid-template-columns: 1fr 1fr;*/ /* Original user value */
            grid-template-columns: auto 1fr; /* MODIFIED: Left column auto-sizes, right takes rest */
            /*gap: 10px;*/ /* Original user value */
            gap: 20px; /* MODIFIED: Adjust this gap to control space between left info and right tables */
            margin-bottom: 10px;
        }

        /* Applied to p tags directly in student-info-left and the new detail-line divs */
        .student-info-left p,
        .student-info-left .detail-line {
            margin: 2px 0;
            font-size: 10px;
        }

        /* Applies to all strong tags within student-info-left for label styling */
        .student-info-left strong {
            display: inline-block;
            min-width: 65px; /* Ensure labels like SN:, Grade: align */
            font-weight: bold; /* Explicitly set bold, though strong usually is */
        }
        /* Override for James Bond's name so it doesn't get min-width */
        .student-info-left > p:first-child > strong {
            min-width: auto;
        }

        /* New CSS for aligning SN/Grade and SSID/Gender lines */
        .student-info-left .detail-line {
            display: flex; /* Use flexbox to position items on the same line */
            /* margin and font-size are covered by the rule above */
        }

        .student-info-left .detail-line .field-group-1 {
            width: 130px; /* MODIFIED: Set a width for the first group (SN/SSID part) */
                          /* Adjust this value to control the starting position of Grade/Gender */
            display: inline-block; /* Allow width to apply */
        }

        .student-info-left .detail-line .field-group-2 {
            display: inline-block; /* For the Grade/Gender part */
        }


        /* Styles for student-info-right and its contents */
        .student-info-right {
            /* This div now only contains the summary-tables-container */
        }

        .summary-tables-container {
            display: flex; /* Remains flex, though only one child table now */
            flex-direction: column;
            gap: 5px;
        }
        .summary-tables-container table {
            border-collapse: collapse;
            font-size: 9px;
            width: auto;
        }
        .summary-tables-container th, .summary-tables-container td {
            border: 1px solid black;
            padding: 3px 4px;
            text-align: left;
            vertical-align: top; /* Consider 'middle' if content looks better centered vertically */
        }
        .summary-tables-container th {
            background-color: #cccccc;
            font-weight: bold;
            /* text-align: center; was handled by class, ensure consistency */
        }
        .right-align { text-align: right; }
        .center-align { text-align: center; }

        .courses-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
            font-size: 9.5px;
        }
        .courses-table th, .courses-table td {
            border: 1px solid black;
            padding: 3px 4px;
            text-align: left;
            vertical-align: top;
        }
        .courses-table > thead > tr > th {
            background-color: #b0b0b0;
            font-weight: bold;
            text-align: center;
        }
        .courses-section-header td {
            font-weight: bold;
            text-align: left !important;
            background-color: #e0e0e0 !important;
            padding-left: 5px !important;
        }
        .course-credits { text-align: right; }
        .course-grades { text-align: center; min-width: 18px; }

        .gpa-summary {
            font-size: 9.5px;
            margin-bottom: 10px;
            line-height: 1.5;
        }
        .gpa-summary span {
            margin-right: 10px;
            display: inline-block;
        }
        .gpa-summary .gpa-group {
            margin-right: 20px;
        }

        .footer-info p {
            font-size: 9px;
            margin-bottom: 10px;
            line-height: 1.2;
        }
        .footer-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            align-items: end;
            margin-bottom: 20px;
            font-size: 9.5px;
        }
        .footer-grid div {
            padding: 0 5px;
        }
        .footer-grid .center-text { text-align: center; font-style: italic; }
        .footer-grid .right-text { text-align: right; }

        .signature-line {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin-top: 25px;
            margin-bottom: 15px;
            font-size: 9.5px;
        }
        .signature-line div {
            border-top: 1px solid black;
            padding-top: 4px;
            text-align: center;
        }
        .accreditation {
            text-align: center;
            font-size: 9.5px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .date-footer {
            text-align: right;
            font-size: 9.5px;
        }
    </style>
</head>
<body>
    <div class="a4-container">
        <div class="header">
            <h1>Student Transcript</h1>
            <p>Sun Canyon High</p>
            <p>435-635-1900</p>
        </div>

        <div class="info-grid">
            <div class="student-info-left">
                <p><strong>James Bond</strong></p>
                <p>007 Spy Ave.</p>
                <p>City, UT 90210</p>
                <br>
                <div class="detail-line">
                    <span class="field-group-1"><strong>SN:</strong> 007007</span>
                    <span class="field-group-2"><strong>Grade:</strong> 12</span>
                </div>
                <div class="detail-line">
                    <span class="field-group-1"><strong>SSID:</strong> 123456</span>
                    <span class="field-group-2"><strong>Gender:</strong> M</span>
                </div>
                <p><strong>Birthdate:</strong> 01/01/2001</p>
                <p><strong>Grad. Date:</strong></p>
                <p><strong>Exit Date:</strong> 05/22/2020</p>
            </div>
            <div class="student-info-right">
                <div class="summary-tables-container">
                    <table class="summary-gpa"> <thead>
                            <tr>
                                <th></th> <th class="center-align">Credit</th>
                                <th class="center-align">GPA</th>
                                <th class="center-align">Testing</th>
                                <th class="center-align">ACT</th>
                                <th class="center-align">SAT</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Cumulative:</td>
                                <td class="right-align">26.583</td>
                                <td class="right-align">3.975</td>
                                <td>Test Date</td>
                                <td class="center-align">09/01/2019</td>
                                <td class="center-align">00/00/0000</td>
                            </tr>
                            <tr>
                                <td>12th:</td>
                                <td class="right-align">1.333</td>
                                <td class="right-align">3.821</td>
                                <td>Composite</td>
                                <td class="center-align">31</td>
                                <td class="center-align">0.0</td>
                            </tr>
                            <tr>
                                <td>11th:</td>
                                <td class="right-align">11.500</td>
                                <td class="right-align">3.955</td>
                                <td>English (e)</td>
                                <td class="center-align">29</td>
                                <td class="center-align">0.0</td>
                            </tr>
                            <tr>
                                <td>10th:</td>
                                <td class="right-align">9.500</td>
                                <td class="right-align">4.000</td>
                                <td>Math (m)</td>
                                <td class="center-align">28</td>
                                <td class="center-align">0.0</td>
                            </tr>
                            <tr>
                                <td>9th:</td>
                                <td class="right-align">4.250</td>
                                <td class="right-align">4.000</td>
                                <td>Reading</td>
                                <td class="center-align">36</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>Class Rank:</td>
                                <td colspan="2" class="center-align">21 of 395</td>
                                <td>Science</td>
                                <td class="center-align">30</td>
                                <td></td>
                            </tr>
                            <tr>
                                <td></td> <td></td>
                                <td></td>
                                <td>English Writing</td> <td class="center-align">0</td>
                                <td class="center-align">0.0</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

    <table class="courses-table">
        <thead>
            <tr>
                <th>School/Course</th>
                <th>Credit</th>
                <th colspan="4">Grades</th>
                <th>School/Course</th>
                <th>Credit</th>
                <th colspan="4">Grades</th>
            </tr>
        </thead>
        <tbody>
            <tr class="courses-section-header">
                <td colspan="12">19-20 Sun Canyon High</td>
            </tr>
            <tr>
                <td>CE ENGL 1010 101S</td>
                <td class="course-credits">0.33</td>
                <td class="course-grades">A-</td><td class="course-grades"></td><td class="course-grades"></td><td class="course-grades"></td>
                <td>Study Skills Chemistry Lab</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">P</td><td class="course-grades">P</td><td class="course-grades">P</td><td class="course-grades">P</td>
            </tr>
            <tr>
                <td>CE BIOL 1010 Lab</td>
                <td class="course-credits">0.25</td>
                <td class="course-grades">A</td><td></td><td></td><td></td>
                <td>Weight Training (9-12)</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td>
            </tr>
            <tr>
                <td>CE DES 1610 Screen Printing</td>
                <td class="course-credits">0.25</td>
                <td class="course-grades">A</td><td></td><td></td><td></td>
                <td>World Civ Honors</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td>
            </tr>
            <tr>
                <td>CE HLTH 1000 Medical Terminology</td>
                <td class="course-credits">0.25</td>
                <td class="course-grades">A</td><td></td><td></td><td></td>
                <td colspan="6"></td>
            </tr>
             <tr>
                <td>Study Skills AP Math Lab</td>
                <td class="course-credits">0.25</td>
                <td class="course-grades">P</td><td></td><td></td><td></td>
                <td colspan="6"></td>
            </tr>
            <tr class="courses-section-header">
                <td colspan="6">18-19 Sun Canyon High</td>
                <td colspan="6">16-17 Sun Canyon Middle</td>
            </tr>
            <tr>
                <td>AP Chemistry</td>
                <td class="course-credits">2.00</td>
                <td class="course-grades">A-</td><td class="course-grades">A</td><td class="course-grades">A-</td><td class="course-grades">A-</td>
                <td>Drivers Ed</td>
                <td class="course-credits">0.25</td>
                <td class="course-grades"></td><td class="course-grades"></td><td class="course-grades"></td><td class="course-grades">A</td>
            </tr>
            <tr>
                <td>AP English 11</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td>
                <td>Biology Honors</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td>
            </tr>
            <tr>
                <td>AP US History</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td>
                <td>Computer Technology</td>
                <td class="course-credits">0.50</td>
                <td></td><td></td><td></td><td></td>
            </tr>
            <tr>
                <td>CE ART 1010</td>
                <td class="course-credits">1.00</td>
                <td></td><td></td><td></td><td></td>
                <td>Construction Technology</td>
                <td class="course-credits">0.50</td>
                <td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td>
            </tr>
            <tr>
                <td>CE CS 1100</td>
                <td class="course-credits">0.50</td>
                <td class="course-grades">A</td><td class="course-grades">A</td><td></td><td></td>
                <td>Geography for Life Honors</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td>
            </tr>
            <tr>
                <td>CE POLS 1100 Amr Gov</td>
                <td class="course-credits">1.00</td>
                <td></td><td></td><td class="course-grades">A</td><td class="course-grades">A</td>
                <td>Keyboarding & Communications Technology</td> <td class="course-credits">0.50</td>
                <td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td>
            </tr>
             <tr>
                <td>Guitar</td>
                <td class="course-credits">0.50</td>
                <td></td><td></td><td></td><td class="course-grades">A</td>
                <td>Lang Arts 9 Honors</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td>
            </tr>
            <tr>
                <td>Spanish II</td>
                <td class="course-credits">1.00</td>
                <td></td><td></td><td></td><td></td>
                <td>Physical Skills</td>
                <td class="course-credits">0.50</td>
                <td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td>
            </tr>
            <tr>
                <td>Physics with Technology</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td>
                <td>Secondary Mathematics 1 Honors</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td>
            </tr>
            <tr>
                <td>Secondary Mathematics 3 Honors</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">A-</td><td class="course-grades">B+</td><td class="course-grades">A-</td><td class="course-grades">A</td>
                <td>Spanish I</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td>
            </tr>
            <tr>
                <td>Study Skills Technology Lab</td>
                <td class="course-credits">0.50</td>
                <td class="course-grades">P</td><td></td><td></td><td></td>
                <td colspan="6"></td>
            </tr>
            <tr>
                <td>Weight Training (9-12)</td>
                <td class="course-credits">0.50</td>
                <td class="course-grades">A</td><td class="course-grades">A</td><td></td><td></td>
                <td colspan="6"></td>
            </tr>
            <tr class="courses-section-header">
                <td colspan="12">18-19 Utah Online School 7-12</td>
            </tr>
            <tr>
                <td>US Gov. Center</td>
                <td class="course-credits">0.50</td>
                <td></td><td></td><td></td><td class="course-grades">A</td>
                <td colspan="6"></td>
            </tr>
            <tr class="courses-section-header">
                <td colspan="12">17-18 Sun Canyon High</td>
            </tr>
            <tr>
                <td>CAD Architectural Design 2</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td>
                <td colspan="6"></td>
            </tr>
            <tr>
                <td>Ceramics I</td>
                <td class="course-credits">1.00</td>
                <td></td><td></td><td class="course-grades">A</td><td class="course-grades">A</td>
                <td colspan="6"></td>
            </tr>
            <tr>
                <td>Chemistry</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td>
                <td colspan="6"></td>
            </tr>
            <tr>
                <td>Digital Graphics Arts Intro: Design</td>
                <td class="course-credits">0.50</td>
                <td class="course-grades">A</td><td class="course-grades">A</td><td></td><td></td>
                <td colspan="6"></td>
            </tr>
            <tr>
                <td>Fitness for Life</td>
                <td class="course-credits">0.50</td>
                <td></td><td></td><td></td><td class="course-grades">A</td>
                <td colspan="6"></td>
            </tr>
            <tr>
                <td>Lang Arts 10 Honors</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td>
                <td colspan="6"></td>
            </tr>
            <tr>
                <td>Secondary Mathematics 2 Honors</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td>
                <td colspan="6"></td>
            </tr>
            <tr>
                <td>Spanish II</td>
                <td class="course-credits">1.00</td>
                <td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td><td class="course-grades">A</td>
                <td colspan="6"></td>
            </tr>
        </tbody>
    </table>

    <div class="gpa-summary">
        <span class="gpa-group">
            <span>09th Q1: 4.000</span> <span>Q2: 4.000</span> <span>Q3: 4.000</span> <span>Q4: 4.000</span> <span>Other: 4.000</span>
        </span>
        <span class="gpa-group">
            <span>11th Q1: 4.000</span> <span>Q2: 4.000</span> <span>Q3: 3.778</span> <span>Q4: 3.963</span> <span>Other:</span>
        </span>
        <br>
        <span class="gpa-group">
            <span>10th Q1: 4.000</span> <span>Q2: 4.000</span> <span>Q3: 4.000</span> <span>Q4: 4.000</span> <span>Other:</span>
        </span>
        <span class="gpa-group">
            <span>12th Q1: 3.821</span> <span>Q2: </span><span>Q3: </span><span>Q4: </span><span>Other:</span>
        </span>
    </div>

    <div class="footer-info">
        <p>All grades are based on a 4.000 scale. Credit and grades are issued quarterly with a typical course earning 0.25 units of credit per quarter or 1.00 credit per year. 28 credits, grades 9-12, are required for graduation.</p>
    </div>

    <div class="footer-grid">
        <div>
            <p style="margin:0;"><strong>Sun Canyon High 1375</strong></p>
            <p style="margin:0;">S. Sand Flow Drive St</p>
            <p style="margin:0;">George, UT 84770</p>
        </div>
        <div class="center-text">Official Electronically Generated Transcript</div> <div class="right-text"></div>
    </div>

    <div class="signature-line">
        <div>Signature</div>
        <div>Title</div>
        <div>Date</div>
    </div>

    <div class="accreditation">
        School Fully Accredited by Northwest Association of Schools and Colleges
    </div>
    <div class="date-footer">
        10/24/19
    </div>
</div> </body>
</html>