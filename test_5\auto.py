import os
import time
import schedule
import pandas as pd
import requests
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import Select
from webdriver_manager.chrome import ChromeDriverManager
from dotenv import load_dotenv
from datetime import datetime

# 加载环境变量
load_dotenv()

class WebAutomation:
    def __init__(self):
        self.url = os.getenv('TARGET_URL')
        self.username = os.getenv('USERNAME')
        self.password = os.getenv('PASSWORD')
        self.output_path = os.getenv('OUTPUT_PATH', 'output')
        self.webhook_url = os.getenv('WEBHOOK_URL')
        
        # 请求头设置
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0'
        }
        
        # 确保输出目录存在
        if not os.path.exists(self.output_path):
            os.makedirs(self.output_path)

    def setup_driver(self):
        """设置Selenium WebDriver"""
        chrome_options = Options()
        # chrome_options.add_argument('--headless')  # 调试时可以注释掉无头模式
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument(f'user-agent={self.headers["User-Agent"]}')
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        return driver

    def login(self):
        """使用Selenium登录目标网页"""
        driver = self.setup_driver()
        try:
            driver.get(self.url)
            
            # 等待登录表单加载
            username_field = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.NAME, "username"))
            )
            password_field = driver.find_element(By.NAME, "password")
            
            # 输入登录信息
            username_field.send_keys(self.username)
            password_field.send_keys(self.password)
            
            # 点击登录按钮
            login_button = driver.find_element(By.XPATH, "//button[@type='submit']")
            login_button.click()
            
            # 等待登录成功
            time.sleep(5)
            
            # 导航到目标页面
            target_link = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.XPATH, "//a[contains(text(), '目标页面')]"))
            )
            target_link.click()
            
            # 等待页面加载
            time.sleep(3)
            
            # 设置筛选条件
            self.apply_filters(driver)
            
            # 获取cookies用于requests
            cookies = driver.get_cookies()
            return cookies, driver.current_url
        except Exception as e:
            print(f"Error during login and navigation: {str(e)}")
            raise
        # 注意：这里不关闭driver，因为后续可能还需要使用

    def apply_filters(self, driver):
        """应用筛选条件"""
        try:
            # 等待筛选表单加载
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.ID, "filter-form"))
            )
            
            # 示例：选择日期范围
            start_date = driver.find_element(By.ID, "start-date")
            end_date = driver.find_element(By.ID, "end-date")
            start_date.clear()
            end_date.clear()
            start_date.send_keys("2024-01-01")
            end_date.send_keys("2024-03-20")
            
            # 示例：选择下拉框选项
            select_element = Select(driver.find_element(By.ID, "category-select"))
            select_element.select_by_visible_text("目标类别")
            
            # 点击筛选按钮
            filter_button = driver.find_element(By.XPATH, "//button[contains(text(), '筛选')]")
            filter_button.click()
            
            # 等待筛选结果加载
            time.sleep(3)
            
        except Exception as e:
            print(f"Error applying filters: {str(e)}")
            raise

    def fetch_data(self, cookies, target_url):
        """使用requests获取页面数据"""
        session = requests.Session()
        
        # 设置cookies，处理cookie的domain和path属性
        for cookie in cookies:
            # 创建cookie字典，只包含必要的属性
            cookie_dict = {
                'name': cookie['name'],
                'value': cookie['value'],
                'domain': cookie.get('domain', ''),
                'path': cookie.get('path', '/')
            }
            session.cookies.set(**cookie_dict)
        
        # 设置请求头
        session.headers.update(self.headers)
        
        try:
            response = session.get(target_url)
            response.raise_for_status()  # 检查响应状态
            return response.text
        except requests.exceptions.RequestException as e:
            print(f"Error fetching data: {str(e)}")
            raise

    def process_data(self, html_content):
        """处理HTML内容并转换为DataFrame"""
        try:
            # 使用pandas读取HTML表格
            dfs = pd.read_html(html_content)
            if dfs:
                df = dfs[0]  # 获取第一个表格
                
                # 数据清洗和处理
                # 示例：删除空行
                df = df.dropna(how='all')
                
                # 示例：重命名列
                # df = df.rename(columns={'原列名': '新列名'})
                
                # 示例：数据类型转换
                # df['日期列'] = pd.to_datetime(df['日期列'])
                
                return df
            return pd.DataFrame()
        except Exception as e:
            print(f"Error processing data: {str(e)}")
            raise

    def save_to_excel(self, df):
        """保存数据到Excel文件"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'data_{timestamp}.xlsx'
        filepath = os.path.join(self.output_path, filename)
        df.to_excel(filepath, index=False)
        return filepath

    def send_to_chat(self, filepath):
        """发送文件到聊天软件"""
        if not self.webhook_url:
            print("Webhook URL not configured")
            return
        
        # 这里需要根据具体的聊天软件API来实现
        # 示例：使用requests发送文件
        with open(filepath, 'rb') as f:
            files = {'file': f}
            response = requests.post(self.webhook_url, files=files)
            if response.status_code == 200:
                print(f"File {filepath} sent successfully")
            else:
                print(f"Failed to send file: {response.text}")

    def run(self):
        """执行完整的自动化流程"""
        try:
            print("Starting automation process...")
            cookies, target_url = self.login()
            html_content = self.fetch_data(cookies, target_url)
            df = self.process_data(html_content)
            filepath = self.save_to_excel(df)
            self.send_to_chat(filepath)
            print("Automation completed successfully")
        except Exception as e:
            print(f"Error during automation: {str(e)}")

def main():
    automation = WebAutomation()
    
    # 设置定时任务（例如每天上午9点运行）
    schedule.every().day.at("09:00").do(automation.run)
    
    # 立即运行一次
    automation.run()
    
    # 保持程序运行
    while True:
        schedule.run_pending()
        time.sleep(60)

if __name__ == "__main__":
    main()
